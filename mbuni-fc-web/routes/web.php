<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PlayerController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\MatchController;
use App\Http\Controllers\GalleryController;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\ContactController;

// Home Page
Route::get('/', [HomeController::class, 'index'])->name('home');

// Team Pages
Route::get('/team', [PlayerController::class, 'index'])->name('team');
Route::get('/team/{player}', [PlayerController::class, 'show'])->name('team.player');

// News Pages
Route::get('/news', [NewsController::class, 'index'])->name('news.index');
Route::get('/news/{news:slug}', [NewsController::class, 'show'])->name('news.show');
Route::get('/news/category/{category:slug}', [NewsController::class, 'category'])->name('news.category');

// Fixtures & Results
Route::get('/fixtures', [MatchController::class, 'index'])->name('fixtures');
Route::get('/fixtures/{match}', [MatchController::class, 'show'])->name('matches.show');

// Gallery
Route::get('/gallery', [GalleryController::class, 'index'])->name('gallery');
Route::get('/gallery/{item}', [GalleryController::class, 'show'])->name('gallery.show');

// About & Club Info
Route::get('/about', [AboutController::class, 'index'])->name('about');
Route::get('/about/history', [AboutController::class, 'history'])->name('about.history');
Route::get('/about/stadium', [AboutController::class, 'stadium'])->name('about.stadium');
Route::get('/about/staff', [AboutController::class, 'staff'])->name('about.staff');

// Fan Zone
Route::get('/fan-zone', [HomeController::class, 'fanZone'])->name('fan-zone');

// Contact
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
