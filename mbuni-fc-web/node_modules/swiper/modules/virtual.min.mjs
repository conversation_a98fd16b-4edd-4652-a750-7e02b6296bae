import{g as getDocument}from"../shared/ssr-window.esm.min.mjs";import{a as setCSSProperty,e as elementChildren,s as setInnerHTML,c as createElement}from"../shared/utils.min.mjs";function Virtual(e){let s,{swiper:t,extendParams:r,on:i,emit:a}=e;r({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}});const l=getDocument();t.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]};const d=l.createElement("div");function n(e,s){const r=t.params.virtual;if(r.cache&&t.virtual.cache[s])return t.virtual.cache[s];let i;return r.renderSlide?(i=r.renderSlide.call(t,e,s),"string"==typeof i&&(setInnerHTML(d,i),i=d.children[0])):i=t.isElement?createElement("swiper-slide"):createElement("div",t.params.slideClass),i.setAttribute("data-swiper-slide-index",s),r.renderSlide||setInnerHTML(i,e),r.cache&&(t.virtual.cache[s]=i),i}function c(e,s,r){const{slidesPerView:i,slidesPerGroup:l,centeredSlides:d,loop:c,initialSlide:o}=t.params;if(s&&!c&&o>0)return;const{addSlidesBefore:u,addSlidesAfter:p}=t.params.virtual,{from:h,to:f,slides:v,slidesGrid:m,offset:g}=t.virtual;t.params.cssMode||t.updateActiveIndex();const E=void 0===r?t.activeIndex||0:r;let x,w,S;x=t.rtlTranslate?"right":t.isHorizontal()?"left":"top",d?(w=Math.floor(i/2)+l+p,S=Math.floor(i/2)+l+u):(w=i+(l-1)+p,S=(c?i:l)+u);let b=E-S,A=E+w;c||(b=Math.max(b,0),A=Math.min(A,v.length-1));let M=(t.slidesGrid[b]||0)-(t.slidesGrid[0]||0);function y(){t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),a("virtualUpdate")}if(c&&E>=S?(b-=S,d||(M+=t.slidesGrid[0])):c&&E<S&&(b=-S,d&&(M+=t.slidesGrid[0])),Object.assign(t.virtual,{from:b,to:A,offset:M,slidesGrid:t.slidesGrid,slidesBefore:S,slidesAfter:w}),h===b&&f===A&&!e)return t.slidesGrid!==m&&M!==g&&t.slides.forEach((e=>{e.style[x]=M-Math.abs(t.cssOverflowAdjustment())+"px"})),t.updateProgress(),void a("virtualUpdate");if(t.params.virtual.renderExternal)return t.params.virtual.renderExternal.call(t,{offset:M,from:b,to:A,slides:function(){const e=[];for(let s=b;s<=A;s+=1)e.push(v[s]);return e}()}),void(t.params.virtual.renderExternalUpdate?y():a("virtualUpdate"));const P=[],j=[],C=e=>{let s=e;return e<0?s=v.length+e:s>=v.length&&(s-=v.length),s};if(e)t.slides.filter((e=>e.matches(`.${t.params.slideClass}, swiper-slide`))).forEach((e=>{e.remove()}));else for(let e=h;e<=f;e+=1)if(e<b||e>A){const s=C(e);t.slides.filter((e=>e.matches(`.${t.params.slideClass}[data-swiper-slide-index="${s}"], swiper-slide[data-swiper-slide-index="${s}"]`))).forEach((e=>{e.remove()}))}const G=c?-v.length:0,I=c?2*v.length:v.length;for(let s=G;s<I;s+=1)if(s>=b&&s<=A){const t=C(s);void 0===f||e?j.push(t):(s>f&&j.push(t),s<h&&P.push(t))}if(j.forEach((e=>{t.slidesEl.append(n(v[e],e))})),c)for(let e=P.length-1;e>=0;e-=1){const s=P[e];t.slidesEl.prepend(n(v[s],s))}else P.sort(((e,s)=>s-e)),P.forEach((e=>{t.slidesEl.prepend(n(v[e],e))}));elementChildren(t.slidesEl,".swiper-slide, swiper-slide").forEach((e=>{e.style[x]=M-Math.abs(t.cssOverflowAdjustment())+"px"})),y()}i("beforeInit",(()=>{if(!t.params.virtual.enabled)return;let e;if(void 0===t.passedParams.virtual.slides){const s=[...t.slidesEl.children].filter((e=>e.matches(`.${t.params.slideClass}, swiper-slide`)));s&&s.length&&(t.virtual.slides=[...s],e=!0,s.forEach(((e,s)=>{e.setAttribute("data-swiper-slide-index",s),t.virtual.cache[s]=e,e.remove()})))}e||(t.virtual.slides=t.params.virtual.slides),t.classNames.push(`${t.params.containerModifierClass}virtual`),t.params.watchSlidesProgress=!0,t.originalParams.watchSlidesProgress=!0,c(!1,!0)})),i("setTranslate",(()=>{t.params.virtual.enabled&&(t.params.cssMode&&!t._immediateVirtual?(clearTimeout(s),s=setTimeout((()=>{c()}),100)):c())})),i("init update resize",(()=>{t.params.virtual.enabled&&t.params.cssMode&&setCSSProperty(t.wrapperEl,"--swiper-virtual-size",`${t.virtualSize}px`)})),Object.assign(t.virtual,{appendSlide:function(e){if("object"==typeof e&&"length"in e)for(let s=0;s<e.length;s+=1)e[s]&&t.virtual.slides.push(e[s]);else t.virtual.slides.push(e);c(!0)},prependSlide:function(e){const s=t.activeIndex;let r=s+1,i=1;if(Array.isArray(e)){for(let s=0;s<e.length;s+=1)e[s]&&t.virtual.slides.unshift(e[s]);r=s+e.length,i=e.length}else t.virtual.slides.unshift(e);if(t.params.virtual.cache){const e=t.virtual.cache,s={};Object.keys(e).forEach((t=>{const r=e[t],a=r.getAttribute("data-swiper-slide-index");a&&r.setAttribute("data-swiper-slide-index",parseInt(a,10)+i),s[parseInt(t,10)+i]=r})),t.virtual.cache=s}c(!0),t.slideTo(r,0)},removeSlide:function(e){if(null==e)return;let s=t.activeIndex;if(Array.isArray(e))for(let r=e.length-1;r>=0;r-=1)t.params.virtual.cache&&(delete t.virtual.cache[e[r]],Object.keys(t.virtual.cache).forEach((s=>{s>e&&(t.virtual.cache[s-1]=t.virtual.cache[s],t.virtual.cache[s-1].setAttribute("data-swiper-slide-index",s-1),delete t.virtual.cache[s])}))),t.virtual.slides.splice(e[r],1),e[r]<s&&(s-=1),s=Math.max(s,0);else t.params.virtual.cache&&(delete t.virtual.cache[e],Object.keys(t.virtual.cache).forEach((s=>{s>e&&(t.virtual.cache[s-1]=t.virtual.cache[s],t.virtual.cache[s-1].setAttribute("data-swiper-slide-index",s-1),delete t.virtual.cache[s])}))),t.virtual.slides.splice(e,1),e<s&&(s-=1),s=Math.max(s,0);c(!0),t.slideTo(s,0)},removeAllSlides:function(){t.virtual.slides=[],t.params.virtual.cache&&(t.virtual.cache={}),c(!0),t.slideTo(0,0)},update:c})}export{Virtual as default};
//# sourceMappingURL=virtual.min.mjs.map