{"version": 3, "file": "effect-cards.mjs.mjs", "names": ["createShadow", "effectInit", "effect<PERSON>arget", "effectVirtualTransitionEnd", "getSlideTransformEl", "EffectCards", "_ref", "swiper", "extendParams", "on", "cardsEffect", "slideShadows", "rotate", "perSlideRotate", "perSlideOffset", "effect", "setTranslate", "slides", "activeIndex", "rtlTranslate", "rtl", "params", "startTranslate", "isTouched", "touchEventsData", "currentTranslate", "translate", "i", "length", "slideEl", "slideProgress", "progress", "Math", "min", "max", "offset", "swiperSlideOffset", "centeredSlides", "cssMode", "wrapperEl", "style", "transform", "minTranslate", "tX", "tY", "tZ", "abs", "scale", "tXAdd", "slideIndex", "virtual", "enabled", "from", "isSwipeToNext", "isSwipeToPrev", "subProgress", "isHorizontal", "prevY", "scaleString", "shadowEl", "querySelector", "opacity", "zIndex", "round", "setTransition", "duration", "transformElements", "map", "for<PERSON>ach", "el", "transitionDuration", "querySelectorAll", "perspective", "overwriteParams", "_loopSwapReset", "watchSlidesProgress", "loopAdditionalSlides", "virtualTranslate"], "sources": ["0"], "mappings": "YAAcA,iBAAoB,8CACpBC,eAAkB,4CAClBC,iBAAoB,8CACpBC,+BAAkC,8DAClCC,wBAA2B,0BAEzC,SAASC,YAAYC,GACnB,IAAIC,OACFA,EAAMC,aACNA,EAAYC,GACZA,GACEH,EACJE,EAAa,CACXE,YAAa,CACXC,cAAc,EACdC,QAAQ,EACRC,eAAgB,EAChBC,eAAgB,KA6FpBb,WAAW,CACTc,OAAQ,QACRR,SACAE,KACAO,aA9FmB,KACnB,MAAMC,OACJA,EAAMC,YACNA,EACAC,aAAcC,GACZb,EACEc,EAASd,EAAOc,OAAOX,aACvBY,eACJA,EAAcC,UACdA,GACEhB,EAAOiB,gBACLC,EAAmBL,GAAOb,EAAOmB,UAAYnB,EAAOmB,UAC1D,IAAK,IAAIC,EAAI,EAAGA,EAAIV,EAAOW,OAAQD,GAAK,EAAG,CACzC,MAAME,EAAUZ,EAAOU,GACjBG,EAAgBD,EAAQE,SACxBA,EAAWC,KAAKC,IAAID,KAAKE,IAAIJ,GAAgB,GAAI,GACvD,IAAIK,EAASN,EAAQO,kBACjB7B,EAAOc,OAAOgB,iBAAmB9B,EAAOc,OAAOiB,UACjD/B,EAAOgC,UAAUC,MAAMC,UAAY,cAAclC,EAAOmC,qBAEtDnC,EAAOc,OAAOgB,gBAAkB9B,EAAOc,OAAOiB,UAChDH,GAAUlB,EAAO,GAAGmB,mBAEtB,IAAIO,EAAKpC,EAAOc,OAAOiB,SAAWH,EAAS5B,EAAOmB,WAAaS,EAC3DS,EAAK,EACT,MAAMC,GAAM,IAAMb,KAAKc,IAAIf,GAC3B,IAAIgB,EAAQ,EACRnC,GAAUS,EAAOR,eAAiBkB,EAClCiB,EAAQ3B,EAAOP,eAAsC,IAArBkB,KAAKc,IAAIf,GAC7C,MAAMkB,EAAa1C,EAAO2C,SAAW3C,EAAOc,OAAO6B,QAAQC,QAAU5C,EAAO2C,QAAQE,KAAOzB,EAAIA,EACzF0B,GAAiBJ,IAAe/B,GAAe+B,IAAe/B,EAAc,IAAMa,EAAW,GAAKA,EAAW,IAAMR,GAAahB,EAAOc,OAAOiB,UAAYb,EAAmBH,EAC7KgC,GAAiBL,IAAe/B,GAAe+B,IAAe/B,EAAc,IAAMa,EAAW,GAAKA,GAAY,IAAMR,GAAahB,EAAOc,OAAOiB,UAAYb,EAAmBH,EACpL,GAAI+B,GAAiBC,EAAe,CAClC,MAAMC,GAAe,EAAIvB,KAAKc,KAAKd,KAAKc,IAAIf,GAAY,IAAO,MAAS,GACxEnB,IAAW,GAAKmB,EAAWwB,EAC3BR,IAAU,GAAMQ,EAChBP,GAAS,GAAKO,EACdX,GAAS,GAAKW,EAAcvB,KAAKc,IAAIf,GAAhC,GACP,CAUA,GAPEY,EAFEZ,EAAW,EAER,QAAQY,OAAQvB,EAAM,IAAM,QAAQ4B,EAAQhB,KAAKc,IAAIf,QACjDA,EAAW,EAEf,QAAQY,OAAQvB,EAAM,IAAM,SAAS4B,EAAQhB,KAAKc,IAAIf,QAEtD,GAAGY,OAELpC,EAAOiD,eAAgB,CAC1B,MAAMC,EAAQb,EACdA,EAAKD,EACLA,EAAKc,CACP,CACA,MAAMC,EAAc3B,EAAW,EAAI,IAAG,GAAK,EAAIgB,GAAShB,GAAa,IAAG,GAAK,EAAIgB,GAAShB,GAGpFU,EAAY,yBACFE,MAAOC,MAAOC,yBAClBxB,EAAOT,OAASQ,GAAOR,EAASA,EAAS,wBAC3C8C,aAIV,GAAIrC,EAAOV,aAAc,CAEvB,IAAIgD,EAAW9B,EAAQ+B,cAAc,wBAChCD,IACHA,EAAW3D,aAAa,QAAS6B,IAE/B8B,IAAUA,EAASnB,MAAMqB,QAAU7B,KAAKC,IAAID,KAAKE,KAAKF,KAAKc,IAAIf,GAAY,IAAO,GAAK,GAAI,GACjG,CACAF,EAAQW,MAAMsB,QAAU9B,KAAKc,IAAId,KAAK+B,MAAMjC,IAAkBb,EAAOW,OACpD1B,aAAamB,EAAQQ,GAC7BW,MAAMC,UAAYA,CAC7B,GAqBAuB,cAnBoBC,IACpB,MAAMC,EAAoB3D,EAAOU,OAAOkD,KAAItC,GAAWzB,oBAAoByB,KAC3EqC,EAAkBE,SAAQC,IACxBA,EAAG7B,MAAM8B,mBAAqB,GAAGL,MACjCI,EAAGE,iBAAiB,wBAAwBH,SAAQT,IAClDA,EAASnB,MAAM8B,mBAAqB,GAAGL,KAAY,GACnD,IAEJ9D,2BAA2B,CACzBI,SACA0D,WACAC,qBACA,EAQFM,YAAa,KAAM,EACnBC,gBAAiB,KAAM,CACrBC,gBAAgB,EAChBC,qBAAqB,EACrBC,qBAAsBrE,EAAOc,OAAOX,YAAYE,OAAS,EAAI,EAC7DyB,gBAAgB,EAChBwC,kBAAmBtE,EAAOc,OAAOiB,WAGvC,QAESjC"}