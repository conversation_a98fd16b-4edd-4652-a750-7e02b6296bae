<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Category;
use App\Models\News;
use App\Models\Player;
use App\Models\FootballMatch;
use App\Models\Staff;
use App\Models\GalleryItem;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
        ]);

        // Create categories
        $newsCategory = Category::create([
            'name' => 'General News',
            'slug' => 'general-news',
            'description' => 'General club news and updates',
            'type' => 'news',
            'color' => '#dc2626',
        ]);

        $matchCategory = Category::create([
            'name' => 'Match Reports',
            'slug' => 'match-reports',
            'description' => 'Match reports and analysis',
            'type' => 'news',
            'color' => '#991b1b',
        ]);

        $galleryCategory = Category::create([
            'name' => 'Team Photos',
            'slug' => 'team-photos',
            'description' => 'Team and match photos',
            'type' => 'gallery',
            'color' => '#dc2626',
        ]);

        // Create sample players
        $players = [
            [
                'name' => 'John Mwangi',
                'jersey_number' => '1',
                'position' => 'goalkeeper',
                'nationality' => 'Tanzania',
                'height' => 1.85,
                'weight' => 78.5,
                'bio' => 'Experienced goalkeeper with excellent reflexes and leadership qualities.',
                'goals_scored' => 0,
                'assists' => 2,
                'matches_played' => 25,
                'is_captain' => false,
                'joined_date' => Carbon::parse('2023-01-15'),
            ],
            [
                'name' => 'Emmanuel Kileo',
                'jersey_number' => '10',
                'position' => 'midfielder',
                'nationality' => 'Tanzania',
                'height' => 1.75,
                'weight' => 70.0,
                'bio' => 'Creative midfielder with excellent passing ability and vision.',
                'goals_scored' => 12,
                'assists' => 8,
                'matches_played' => 28,
                'is_captain' => true,
                'joined_date' => Carbon::parse('2022-06-01'),
            ],
            [
                'name' => 'David Moshi',
                'jersey_number' => '9',
                'position' => 'forward',
                'nationality' => 'Tanzania',
                'height' => 1.80,
                'weight' => 75.0,
                'bio' => 'Clinical striker with a keen eye for goal and strong finishing ability.',
                'goals_scored' => 18,
                'assists' => 5,
                'matches_played' => 26,
                'is_captain' => false,
                'joined_date' => Carbon::parse('2023-03-10'),
            ],
            [
                'name' => 'Peter Massawe',
                'jersey_number' => '4',
                'position' => 'defender',
                'nationality' => 'Tanzania',
                'height' => 1.82,
                'weight' => 80.0,
                'bio' => 'Solid defender with strong aerial ability and tactical awareness.',
                'goals_scored' => 3,
                'assists' => 1,
                'matches_played' => 27,
                'is_captain' => false,
                'joined_date' => Carbon::parse('2022-08-20'),
            ],
        ];

        foreach ($players as $playerData) {
            Player::create($playerData);
        }

        // Create sample news articles
        $newsArticles = [
            [
                'title' => 'Mbuni FC Wins Derby Against Local Rivals',
                'slug' => 'mbuni-fc-wins-derby-against-local-rivals',
                'excerpt' => 'In a thrilling match that kept fans on the edge of their seats, Mbuni FC secured a 2-1 victory against their local rivals.',
                'content' => 'In a thrilling match that kept fans on the edge of their seats, Mbuni FC secured a 2-1 victory against their local rivals in front of a packed stadium. The match showcased the best of Tanzanian football with both teams displaying exceptional skill and determination. Goals from David Moshi and Emmanuel Kileo sealed the victory for Mbuni FC, with Moshi opening the scoring in the 23rd minute with a brilliant header from a corner kick. The opposition equalized just before halftime, but Kileo\'s stunning long-range effort in the 67th minute proved to be the winner.',
                'category_id' => $matchCategory->id,
                'tags' => ['derby', 'victory', 'local rivals'],
                'is_featured' => true,
                'is_published' => true,
                'published_at' => Carbon::now()->subDays(2),
                'views_count' => 245,
            ],
            [
                'title' => 'New Training Facility Opens in Arusha',
                'slug' => 'new-training-facility-opens-in-arusha',
                'excerpt' => 'Mbuni FC proudly announces the opening of our state-of-the-art training facility, marking a new chapter in the club\'s development.',
                'content' => 'Mbuni FC proudly announces the opening of our state-of-the-art training facility, marking a new chapter in the club\'s development. The new facility features modern equipment, multiple training pitches, and recovery areas designed to help our players reach their full potential. This investment demonstrates our commitment to excellence and our vision for the future of football in Arusha.',
                'category_id' => $newsCategory->id,
                'tags' => ['training', 'facility', 'development'],
                'is_featured' => false,
                'is_published' => true,
                'published_at' => Carbon::now()->subDays(5),
                'views_count' => 156,
            ],
            [
                'title' => 'Youth Academy Produces Rising Stars',
                'slug' => 'youth-academy-produces-rising-stars',
                'excerpt' => 'Our youth academy continues to develop talented young players who are making their mark in Tanzanian football.',
                'content' => 'Our youth academy continues to develop talented young players who are making their mark in Tanzanian football. With a focus on technical skills, tactical understanding, and character development, our academy is producing the next generation of football stars. Several academy graduates have already made their debut for the senior team this season.',
                'category_id' => $newsCategory->id,
                'tags' => ['youth', 'academy', 'development'],
                'is_featured' => true,
                'is_published' => true,
                'published_at' => Carbon::now()->subDays(7),
                'views_count' => 189,
            ],
        ];

        foreach ($newsArticles as $articleData) {
            News::create($articleData);
        }

        // Create sample matches
        $matches = [
            [
                'opponent_name' => 'Arusha United',
                'match_date' => Carbon::now()->addDays(7)->setTime(15, 0),
                'venue' => 'Mbuni Stadium, Arusha',
                'is_home_match' => true,
                'competition' => 'league',
                'status' => 'scheduled',
            ],
            [
                'opponent_name' => 'Kilimanjaro FC',
                'match_date' => Carbon::now()->addDays(14)->setTime(16, 0),
                'venue' => 'Kilimanjaro Stadium',
                'is_home_match' => false,
                'competition' => 'cup',
                'status' => 'scheduled',
            ],
            [
                'opponent_name' => 'Moshi Warriors',
                'match_date' => Carbon::now()->subDays(7)->setTime(15, 0),
                'venue' => 'Mbuni Stadium, Arusha',
                'is_home_match' => true,
                'competition' => 'league',
                'status' => 'finished',
                'mbuni_score' => 2,
                'opponent_score' => 1,
                'match_highlights' => 'Great performance with goals from Moshi and Kileo',
            ],
        ];

        foreach ($matches as $matchData) {
            FootballMatch::create($matchData);
        }

        // Create sample staff
        $staffMembers = [
            [
                'name' => 'Joseph Mwalimu',
                'position' => 'Head Coach',
                'department' => 'coaching',
                'bio' => 'Experienced coach with over 15 years in Tanzanian football.',
                'nationality' => 'Tanzania',
                'joined_date' => Carbon::parse('2022-01-01'),
                'qualifications' => ['CAF A License', 'UEFA B License'],
                'sort_order' => 1,
            ],
            [
                'name' => 'Michael Temba',
                'position' => 'Assistant Coach',
                'department' => 'coaching',
                'bio' => 'Former professional player turned coach with tactical expertise.',
                'nationality' => 'Tanzania',
                'joined_date' => Carbon::parse('2022-06-01'),
                'qualifications' => ['CAF B License'],
                'sort_order' => 2,
            ],
        ];

        foreach ($staffMembers as $staffData) {
            Staff::create($staffData);
        }

        $this->command->info('Database seeded successfully!');
    }
}
